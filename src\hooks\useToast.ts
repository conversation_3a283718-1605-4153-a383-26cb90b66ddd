import { useRef } from 'react';
import { Toast } from 'primereact/toast';
import { ToastMessage } from '../types/common';

export const useToast = () => {
  const toast = useRef<Toast>(null);

  const showToast = (message: ToastMessage) => {
    toast.current?.show({
      severity: message.severity,
      summary: message.summary,
      detail: message.detail,
      life: message.life || 3000
    });
  };

  const showSuccess = (summary: string, detail?: string) => {
    showToast({ severity: 'success', summary, detail });
  };

  const showError = (summary: string, detail?: string) => {
    showToast({ severity: 'error', summary, detail });
  };

  const showInfo = (summary: string, detail?: string) => {
    showToast({ severity: 'info', summary, detail });
  };

  const showWarn = (summary: string, detail?: string) => {
    showToast({ severity: 'warn', summary, detail });
  };

  return {
    toast,
    showToast,
    showSuccess,
    showError,
    showInfo,
    showWarn
  };
};
