export interface User {
  id: number;
  name: string;
  username: string;
  password: string;
  userRole: string;
}

export class UserModel implements User {
  id: number;
  name: string;
  username: string;
  password: string;
  userRole: string;

  constructor(id: number, name: string, username: string, password: string, userRole: string) {
    this.id = id;
    this.name = name;
    this.username = username;
    this.password = password;
    this.userRole = userRole;
  }
}
