$colors: (
    "blue": #0d6efd,
    "green": #198754,
    "yellow": #ffc107,
    "cyan": #0dcaf0,
    "pink": #d63384,
    "indigo": #6610f2,
    "teal": #20c997,
    "orange": #fd7e14,
    "bluegray": #7C8EA7,
    "purple": #6f42c1,
    "red": #DC3545,
    "primary": $primaryColor
);

//shades
$shade000:rgba(255,255,255,.87) !default;  //text color
$shade100:rgba(255,255,255,.60) !default;  //text secondary color
$shade200:#7789a1 !default;
$shade300:#687c97 !default;
$shade500:#525d6c !default;
$shade600:#3f4b5b !default;  //input bg, border, divider
$shade700:#343e4d !default;
$shade800:#2a323d !default;  //elevated surface
$shade900:#20262e !default;  //ground surface

$hoverBg:rgba(255,255,255,.04) !default;

//global
$fontFamily:-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol" !default;
$fontSize:1rem !default;
$fontWeight:normal !default;
$textColor:$shade000 !default;
$textSecondaryColor:$shade100 !default;
$borderRadius:4px !default;
$transitionDuration:.15s !default;
$formElementTransition:background-color $transitionDuration, border-color $transitionDuration, box-shadow $transitionDuration !default;
$actionIconTransition:color $transitionDuration, box-shadow $transitionDuration !default;
$listItemTransition:box-shadow $transitionDuration !default;
$primeIconFontSize:1rem !default;
$divider:1px solid $shade600 !default;
$inlineSpacing:.5rem !default;
$disabledOpacity:.65 !default;
$maskBg:rgba(0, 0, 0, 0.4) !default;
$loadingIconFontSize:2rem !default;
$errorColor:#f19ea6 !default;

//scale
$scaleSM:0.875 !default;
$scaleLG:1.25 !default;

//focus
$focusOutlineColor:$primaryLightColor !default;
$focusOutline:0 none !default;
$focusOutlineOffset:0 !default;
$focusShadow:0 0 0 1px $focusOutlineColor !default;

//action icons
$actionIconWidth: 2rem !default;
$actionIconHeight: 2rem !default;
$actionIconBg: transparent !default;
$actionIconBorder: 0 none !default;
$actionIconColor: $shade100 !default;
$actionIconHoverBg: transparent !default;
$actionIconHoverBorderColor: transparent !default;
$actionIconHoverColor: $shade000 !default;
$actionIconBorderRadius: 50% !default;

//input field (e.g. inputtext, spinner, inputmask)
$inputPadding:.5rem .75rem !default;
$inputTextFontSize:1rem !default;
$inputBg:$shade900 !default;
$inputTextColor:$shade000 !default;
$inputIconColor:$shade100 !default;
$inputBorder:1px solid $shade600 !default;
$inputHoverBorderColor:$shade600 !default;
$inputFocusBorderColor:$primaryColor !default;
$inputErrorBorderColor:$errorColor !default;
$inputPlaceholderTextColor:$shade100 !default;
$inputFilledBg:$shade600 !default;
$inputFilledHoverBg:$shade600 !default;
$inputFilledFocusBg:$shade600 !default;

//input groups
$inputGroupBg:$shade800 !default;
$inputGroupTextColor:$shade100 !default;
$inputGroupAddOnMinWidth:2.357rem !default;

//input lists (e.g. dropdown, autocomplete, multiselect, orderlist)
$inputListBg:$shade800 !default;
$inputListTextColor:$shade000 !default;
$inputListBorder:$inputBorder !default;
$inputListPadding:.5rem 0 !default;
$inputListItemPadding:.5rem 1.5rem !default;
$inputListItemBg:transparent !default;
$inputListItemTextColor:$shade000 !default;
$inputListItemHoverBg:$hoverBg !default;
$inputListItemTextHoverColor:$shade000 !default;
$inputListItemFocusBg:rgba(255,255,255,.12) !default;
$inputListItemTextFocusColor:$shade000 !default;
$inputListItemBorder:0 none !default;
$inputListItemBorderRadius:0 !default;
$inputListItemMargin:0 !default;
$inputListItemFocusShadow:inset 0 0 0 0.15rem $focusOutlineColor !default;
$inputListHeaderPadding:.75rem 1.5rem !default;
$inputListHeaderMargin:0 !default;
$inputListHeaderBg:$shade800 !default;
$inputListHeaderTextColor:$shade000 !default;
$inputListHeaderBorder:1px solid $shade600 !default;

//inputs with overlays (e.g. autocomplete, dropdown, multiselect)
$inputOverlayBg:$inputListBg !default;
$inputOverlayHeaderBg:$inputListHeaderBg !default;
$inputOverlayBorder:1px solid $shade600 !default;
$inputOverlayShadow:none !default;

//password
$passwordMeterBg:$shade600 !default;
$passwordWeakBg:#f19ea6 !default;
$passwordMediumBg:#ffe082 !default;
$passwordStrongBg:#9fdaa8 !default;

//button
$buttonPadding:.5rem .75rem !default;
$buttonIconOnlyWidth:2.357rem !default;
$buttonIconOnlyPadding:.5rem 0 !default;
$buttonBg:$primaryColor !default;
$buttonTextColor:$primaryTextColor !default;
$buttonBorder:1px solid $primaryColor !default;
$buttonHoverBg:$primaryDarkColor !default;
$buttonTextHoverColor:$primaryTextColor !default;
$buttonHoverBorderColor:$primaryDarkColor !default;
$buttonActiveBg:$primaryDarkerColor !default;
$buttonTextActiveColor:$primaryTextColor !default;
$buttonActiveBorderColor:$primaryDarkerColor !default;
$raisedButtonShadow:0 3px 1px -2px rgba(0,0,0,.2), 0 2px 2px 0 rgba(0,0,0,.14), 0 1px 5px 0 rgba(0,0,0,.12) !default;
$roundedButtonBorderRadius:2rem !default;

$textButtonHoverBgOpacity:.04 !default;
$textButtonActiveBgOpacity:.16 !default;
$outlinedButtonBorder:1px solid !default;
$plainButtonTextColor:$textSecondaryColor !default;
$plainButtonHoverBgColor:$hoverBg !default;
$plainButtonActiveBgColor:rgba(255,255,255,.16) !default;

$secondaryButtonBg:#6c757d !default;
$secondaryButtonTextColor:#ffffff !default;
$secondaryButtonBorder:1px solid #6c757d !default;
$secondaryButtonHoverBg:#5a6268 !default;
$secondaryButtonTextHoverColor:#ffffff !default;
$secondaryButtonHoverBorderColor:#5a6268 !default;
$secondaryButtonActiveBg:#545b62 !default;
$secondaryButtonTextActiveColor:#ffffff !default;
$secondaryButtonActiveBorderColor:#4e555b !default;
$secondaryButtonFocusShadow:0 0 0 1px rgba(130,138,145,.5) !default;

$infoButtonBg:#7fd8e6 !default;
$infoButtonTextColor:#151515 !default;
$infoButtonBorder:1px solid #4cc8db !default;
$infoButtonHoverBg:#4cc8db !default;
$infoButtonTextHoverColor:#151515 !default;
$infoButtonHoverBorderColor:#26bdd3 !default;
$infoButtonActiveBg:#26bdd3 !default;
$infoButtonTextActiveColor:#151515 !default;
$infoButtonActiveBorderColor:#00b2cc !default;
$infoButtonFocusShadow:0 0 0 1px #b1e8f0 !default;

$successButtonBg:#9fdaa8 !default;
$successButtonTextColor:#151515 !default;
$successButtonBorder:1px solid #78cc86 !default;
$successButtonHoverBg:#78cc86 !default;
$successButtonTextHoverColor:#151515 !default;
$successButtonHoverBorderColor:#5ac06c !default;
$successButtonActiveBg:#5ac06c !default;
$successButtonTextActiveColor:#151515 !default;
$successButtonActiveBorderColor:#3cb553 !default;
$successButtonFocusShadow:0 0 0 1px #c5e8ca !default;

$warningButtonBg:#ffe082 !default;
$warningButtonTextColor:#151515 !default;
$warningButtonBorder:1px solid #ffd54f !default;
$warningButtonHoverBg:#ffd54f !default;
$warningButtonTextHoverColor:#151515 !default;
$warningButtonHoverBorderColor:#ffca28 !default;
$warningButtonActiveBg:#ffca28 !default;
$warningButtonTextActiveColor:#151515 !default;
$warningButtonActiveBorderColor:#ffc107 !default;
$warningButtonFocusShadow:0 0 0 1px #ffecb3 !default;

$helpButtonBg:#b7a2e0 !default;
$helpButtonTextColor:#151515 !default;
$helpButtonBorder:1px solid #9a7cd4 !default;
$helpButtonHoverBg:#9a7cd4 !default;
$helpButtonTextHoverColor:#151515 !default;
$helpButtonHoverBorderColor:#845fca !default;
$helpButtonActiveBg:#845fca !default;
$helpButtonTextActiveColor:#151515 !default;
$helpButtonActiveBorderColor:#6d43c0 !default;
$helpButtonFocusShadow:0 0 0 1px #d3c7ec !default;

$dangerButtonBg:#f19ea6 !default;
$dangerButtonTextColor:#151515 !default;
$dangerButtonBorder:1px solid #e97984 !default;
$dangerButtonHoverBg:#e97984 !default;
$dangerButtonTextHoverColor:#151515 !default;
$dangerButtonHoverBorderColor:#f75965 !default;
$dangerButtonActiveBg:#f75965 !default;
$dangerButtonTextActiveColor:#151515 !default;
$dangerButtonActiveBorderColor:#fd464e !default;
$dangerButtonFocusShadow:0 0 0 1px #ffd0d9 !default;

$linkButtonColor:$primaryColor !default;
$linkButtonHoverColor:$primaryDarkColor !default;
$linkButtonTextHoverDecoration:underline !default;
$linkButtonFocusShadow:0 0 0 1px $focusOutlineColor !default;

//checkbox
$checkboxWidth:20px !default;
$checkboxHeight:20px !default;
$checkboxBorder:1px solid $shade600 !default;
$checkboxIconFontSize:14px !default;
$checkboxActiveBorderColor:$primaryColor !default;
$checkboxActiveBg:$primaryColor !default;
$checkboxIconActiveColor:$primaryTextColor !default;
$checkboxActiveHoverBg:$primaryDarkerColor !default;
$checkboxIconActiveHoverColor:$primaryTextColor !default;
$checkboxActiveHoverBorderColor:$primaryDarkerColor !default;

//radiobutton
$radiobuttonWidth:20px !default;
$radiobuttonHeight:20px !default;
$radiobuttonBorder:1px solid $shade600 !default;
$radiobuttonIconSize:12px !default;
$radiobuttonActiveBorderColor:$primaryColor !default;
$radiobuttonActiveBg:$primaryColor !default;
$radiobuttonIconActiveColor:$primaryTextColor !default;
$radiobuttonActiveHoverBg:$primaryDarkerColor !default;
$radiobuttonIconActiveHoverColor:$primaryTextColor !default;
$radiobuttonActiveHoverBorderColor:$primaryDarkerColor !default;

//colorpicker
$colorPickerPreviewWidth:2rem !default;
$colorPickerPreviewHeight:2rem !default;
$colorPickerBg:$shade800 !default;
$colorPickerBorder:1px solid $shade600 !default;
$colorPickerHandleColor:$shade000 !default;

//togglebutton
$toggleButtonBg:#6c757d !default;
$toggleButtonBorder:1px solid #6c757d !default;
$toggleButtonTextColor:#ffffff !default;
$toggleButtonIconColor:#ffffff !default;
$toggleButtonHoverBg:#5a6268 !default;
$toggleButtonHoverBorderColor:#545b62 !default;
$toggleButtonTextHoverColor:#ffffff !default;
$toggleButtonIconHoverColor:#ffffff !default;
$toggleButtonActiveBg:#545b62 !default;
$toggleButtonActiveBorderColor:#4e555b !default;
$toggleButtonTextActiveColor:#ffffff !default;
$toggleButtonIconActiveColor:#ffffff !default;
$toggleButtonActiveHoverBg:#545b62 !default;
$toggleButtonActiveHoverBorderColor:#4e555b !default;
$toggleButtonTextActiveHoverColor:#ffffff !default;
$toggleButtonIconActiveHoverColor:#ffffff !default;

//inplace
$inplacePadding:$inputPadding !default;
$inplaceHoverBg:$hoverBg !default;
$inplaceTextHoverColor:$shade000 !default;

//rating
$ratingIconFontSize:1.143rem !default;
$ratingCancelIconColor:#f19ea6 !default;
$ratingCancelIconHoverColor:#f19ea6 !default;
$ratingStarIconOffColor:$shade000 !default;
$ratingStarIconOnColor:$primaryColor !default;
$ratingStarIconHoverColor:$primaryColor !default;

//slider
$sliderBg:$shade600 !default;
$sliderBorder:0 none !default;
$sliderHorizontalHeight:.286rem !default;
$sliderVerticalWidth:0.286rem !default;
$sliderHandleWidth:1.143rem !default;
$sliderHandleHeight:1.143rem !default;
$sliderHandleBg:$primaryColor !default;
$sliderHandleBorder:2px solid $primaryColor !default;
$sliderHandleBorderRadius:$borderRadius !default;
$sliderHandleHoverBorderColor:$primaryDarkColor !default;
$sliderHandleHoverBg:$primaryDarkColor !default;
$sliderRangeBg:$primaryColor !default;

//calendar
$calendarTableMargin:.5rem 0 !default;
$calendarPadding:0 !default;
$calendarBg:$shade800 !default;
$calendarInlineBg:$calendarBg !default;
$calendarTextColor:$shade000 !default;
$calendarBorder:$inputListBorder !default;
$calendarOverlayBorder:$inputOverlayBorder !default;

$calendarHeaderPadding:.5rem !default;
$calendarHeaderBg:$shade800 !default;
$calendarInlineHeaderBg:$calendarBg !default;
$calendarHeaderBorder:1px solid $shade600 !default;
$calendarHeaderTextColor:$shade000 !default;
$calendarHeaderFontWeight:600 !default;
$calendarHeaderCellPadding:.5rem !default;
$calendarMonthYearHeaderHoverTextColor: $primaryColor !default;

$calendarCellDatePadding:.5rem !default;
$calendarCellDateWidth:2.5rem !default;
$calendarCellDateHeight:2.5rem !default;
$calendarCellDateBorderRadius:$borderRadius !default;
$calendarCellDateBorder:1px solid transparent !default;
$calendarCellDateHoverBg:$hoverBg !default;
$calendarCellDateTodayBg:transparent !default;
$calendarCellDateTodayBorderColor:transparent !default;
$calendarCellDateTodayTextColor:$primaryColor !default;

$calendarButtonBarPadding:1rem 0 !default;
$calendarTimePickerPadding:.5rem !default;
$calendarTimePickerElementPadding:0 .5rem !default;
$calendarTimePickerTimeFontSize:1.25rem !default;

$calendarBreakpoint:769px !default;
$calendarCellDatePaddingSM:0 !default;

//input switch
$inputSwitchWidth:3rem !default;
$inputSwitchHeight:1.75rem !default;
$inputSwitchBorderRadius:$borderRadius !default;
$inputSwitchHandleWidth:1.250rem !default;
$inputSwitchHandleHeight:1.250rem !default;
$inputSwitchHandleBorderRadius:$borderRadius !default;
$inputSwitchSliderPadding:.25rem !default;
$inputSwitchSliderOffBg:$shade600 !default;
$inputSwitchHandleOffBg:$shade100 !default;
$inputSwitchSliderOffHoverBg:$shade600 !default;
$inputSwitchSliderOnBg:$primaryColor !default;
$inputSwitchSliderOnHoverBg:$primaryColor !default;
$inputSwitchHandleOnBg:$primaryTextColor !default;

//panel
$panelHeaderBorderColor: $shade600 !default;
$panelHeaderBorder:1px solid $shade600 !default;
$panelHeaderBg:$shade800 !default;
$panelHeaderTextColor:$shade000 !default;
$panelHeaderFontWeight:600 !default;
$panelHeaderPadding:1rem 1.25rem !default;
$panelToggleableHeaderPadding:.5rem 1.25rem !default;

$panelHeaderHoverBg:$hoverBg !default;
$panelHeaderHoverBorderColor:$shade600 !default;
$panelHeaderTextHoverColor:$shade000 !default;

$panelContentBorderColor: $shade600 !default;
$panelContentBorder:1px solid $shade600 !default;
$panelContentBg:$shade800 !default;
$panelContentEvenRowBg:rgba(255,255,255,.02) !default;
$panelContentTextColor:$shade000 !default;
$panelContentPadding:1.25rem !default;

$panelFooterBorder:1px solid $shade600 !default;
$panelFooterBg:$shade800 !default;
$panelFooterTextColor:$shade000 !default;
$panelFooterPadding:.5rem 1.25rem !default;

//accordion
$accordionSpacing:0 !default;
$accordionHeaderBorder:$panelHeaderBorder !default;
$accordionHeaderBg:$panelHeaderBg !default;
$accordionHeaderTextColor:$panelHeaderTextColor !default;
$accordionHeaderFontWeight:$panelHeaderFontWeight !default;
$accordionHeaderPadding:$panelHeaderPadding !default;

$accordionHeaderHoverBg:$hoverBg !default;
$accordionHeaderHoverBorderColor:$shade600 !default;
$accordionHeaderTextHoverColor:$shade000 !default;

$accordionHeaderActiveBg:$panelHeaderBg !default;
$accordionHeaderActiveBorderColor:$shade600 !default;
$accordionHeaderTextActiveColor:$shade000 !default;

$accordionHeaderActiveHoverBg:$hoverBg !default;
$accordionHeaderActiveHoverBorderColor:$shade600 !default;
$accordionHeaderTextActiveHoverColor:$shade000 !default;

$accordionContentBorder:$panelContentBorder !default;
$accordionContentBg:$panelContentBg !default;
$accordionContentTextColor:$panelContentTextColor !default;
$accordionContentPadding:$panelContentPadding !default;

//tabview
$tabviewNavBorder:1px solid $shade600 !default;
$tabviewNavBorderWidth:0 0 1px 0 !default;
$tabviewNavBg:transparent !default;

$tabviewHeaderSpacing:0 !default;
$tabviewHeaderBorder:solid !default;
$tabviewHeaderBorderWidth:1px !default;
$tabviewHeaderBorderColor:$shade800 $shade800 $shade600 $shade800 !default;
$tabviewHeaderBg:$shade800 !default;
$tabviewHeaderTextColor:$shade100 !default;
$tabviewHeaderFontWeight:$panelHeaderFontWeight !default;
$tabviewHeaderPadding:.75rem 1rem !default;
$tabviewHeaderMargin:0 0 -1px 0 !default;

$tabviewHeaderHoverBg:$shade800 !default;
$tabviewHeaderHoverBorderColor:$shade600 !default;
$tabviewHeaderTextHoverColor:$shade000 !default;

$tabviewHeaderActiveBg:$shade800 !default;
$tabviewHeaderActiveBorderColor:$shade600 $shade600 $shade800 $shade600 !default;
$tabviewHeaderTextActiveColor:$shade100 !default;

$tabviewContentBorder:0 none !default;
$tabviewContentBg:$panelContentBg !default;
$tabviewContentTextColor:$panelContentTextColor !default;
$tabviewContentPadding:$panelContentPadding !default;

//upload
$fileUploadProgressBarHeight:.25rem !default;
$fileUploadContentPadding: 2rem 1rem !default;
$fileUploadContentHoverBorder: 1px dashed $primaryColor !default;
$fileUploadFileBorder: 1px solid $shade600 !default;
$fileUploadFilePadding: 1rem !default;

//scrollpanel
$scrollPanelTrackBorder:0 none !default;
$scrollPanelTrackBg:$shade600 !default;

//card
$cardBodyPadding:1.5rem !default;
$cardTitleFontSize:1.5rem !default;
$cardTitleFontWeight:700 !default;
$cardSubTitleFontWeight:400 !default;
$cardSubTitleColor:$shade100 !default;
$cardContentPadding:1rem 0 !default;
$cardFooterPadding:1rem 0 0 0 !default;
$cardShadow:0 2px 1px -1px rgba(0,0,0,.2), 0 1px 1px 0 rgba(0,0,0,.14), 0 1px 3px 0 rgba(0,0,0,.12) !default;

//editor
$editorToolbarBg:$panelHeaderBg !default;
$editorToolbarBorder:$panelHeaderBorder !default;
$editorToolbarPadding:$panelHeaderPadding !default;
$editorToolbarIconColor:$textSecondaryColor !default;
$editorToolbarIconHoverColor:$textColor !default;
$editorIconActiveColor:$primaryColor !default;
$editorContentBorder:$panelContentBorder !default;
$editorContentBg:$panelContentBg !default;

//paginator
$paginatorBg:$shade800 !default;
$paginatorTextColor:$primaryColor !default;
$paginatorBorder:solid $shade600 !default;
$paginatorBorderWidth:0 !default;
$paginatorPadding:.75rem !default;
$paginatorElementWidth:$buttonIconOnlyWidth !default;
$paginatorElementHeight:$buttonIconOnlyWidth !default;
$paginatorElementBg:transparent !default;
$paginatorElementBorder:1px solid $shade600 !default;
$paginatorElementIconColor:$primaryColor !default;
$paginatorElementHoverBg:$hoverBg !default;
$paginatorElementHoverBorderColor:$shade600 !default;
$paginatorElementIconHoverColor:$primaryColor !default;
$paginatorElementBorderRadius:0 !default;
$paginatorElementMargin:0 0 0 -1px !default;
$paginatorElementPadding:0 !default;

//table
$tableHeaderBorder:solid $shade600 !default;
$tableHeaderBorderWidth:1px 0 0 0 !default;
$tableHeaderBg:$shade800 !default;
$tableHeaderTextColor:$shade100 !default;
$tableHeaderFontWeight:600 !default;
$tableHeaderPadding:1rem 1rem !default;

$tableHeaderCellPadding:1rem 1rem !default;
$tableHeaderCellBg:$shade800 !default;
$tableHeaderCellTextColor:$shade000 !default;
$tableHeaderCellFontWeight:600 !default;
$tableHeaderCellBorder:1px solid $shade600 !default;
$tableHeaderCellBorderWidth:1px 0 2px 0 !default;
$tableHeaderCellHoverBg:$hoverBg !default;
$tableHeaderCellTextHoverColor:$shade000 !default;
$tableHeaderCellIconColor:$shade100 !default;
$tableHeaderCellIconHoverColor:$shade000 !default;
$tableHeaderCellHighlightBg:$shade800 !default;
$tableHeaderCellHighlightTextColor:$primaryColor !default;
$tableHeaderCellHighlightHoverBg:$hoverBg !default;
$tableHeaderCellHighlightTextHoverColor:$primaryColor !default;
$tableSortableColumnBadgeSize:1.143rem !default;

$tableBodyRowBg:$shade800 !default;
$tableBodyRowTextColor:$shade000 !default;
$tableBodyRowEvenBg:#2f3641 !default;
$tableBodyRowHoverBg:$hoverBg !default;
$tableBodyRowTextHoverColor:$shade000 !default;
$tableBodyCellBorder:1px solid $shade600 !default;
$tableBodyCellBorderWidth:1px 0 0 0 !default;
$tableBodyCellPadding:1rem 1rem !default;

$tableFooterCellPadding:1rem 1rem !default;
$tableFooterCellBg:$shade800 !default;
$tableFooterCellTextColor:$shade000 !default;
$tableFooterCellFontWeight:600 !default;
$tableFooterCellBorder:1px solid $shade600 !default;
$tableFooterCellBorderWidth:1px 0 1px 0 !default;
$tableResizerHelperBg:$primaryColor !default;

$tableFooterBorder:1px solid $shade600 !default;
$tableFooterBorderWidth:1px 0 1px 0 !default;
$tableFooterBg:$shade800 !default;
$tableFooterTextColor:$shade000 !default;
$tableFooterFontWeight:600 !default;
$tableFooterPadding:1rem 1rem !default;

$tableCellContentAlignment:left !default;
$tableTopPaginatorBorderWidth:0 !default;
$tableBottomPaginatorBorderWidth:1px 0 0 0 !default;

$tableScaleSM:0.5 !default;
$tableScaleLG:1.25 !default;

//dataview
$dataViewContentPadding:0 !default;
$dataViewContentBorder:0 none !default;
$dataViewListItemBorder:1px solid $shade600 !default;
$dataViewListItemBorderWidth:1px 0 0 0 !default;

//schedule
$fullCalendarEventBg:$primaryDarkColor !default;
$fullCalendarEventBorderColor:$primaryDarkColor !default;
$fullCalendarEventBorder:1px solid $primaryDarkColor !default;
$fullCalendarEventTextColor:$primaryTextColor !default;

//tree
$treeContainerPadding:0.286rem !default;
$treeNodePadding:0.143rem !default;
$treeNodeContentPadding:0.286rem !default;
$treeNodeChildrenPadding:0 0 0 1rem !default;
$treeNodeIconColor:$shade600 !default;

//timeline
$timelineVerticalEventContentPadding:0 1rem !default;
$timelineHorizontalEventContentPadding:1rem 0 !default;
$timelineEventMarkerWidth:1rem !default;
$timelineEventMarkerHeight:1rem !default;
$timelineEventMarkerBorderRadius:50% !default;
$timelineEventMarkerBorder:0 none !default;
$timelineEventMarkerBackground:$primaryColor !default;
$timelineEventConnectorSize:2px !default;
$timelineEventColor:$shade600 !default;

//org chart
$organizationChartConnectorColor:$shade600 !default;

//message
$messageMargin:1rem 0 !default;
$messagePadding:1rem 1.25rem !default;
$messageBorderWidth:1px !default;
$messageIconFontSize:1.5rem !default;
$messageTextFontSize:1rem !default;
$messageTextFontWeight:500 !default;

//inline message
$inlineMessagePadding:$inputPadding !default;
$inlineMessageMargin:0 !default;
$inlineMessageIconFontSize:1rem !default;
$inlineMessageTextFontSize:1rem !default;
$inlineMessageBorderWidth:0px !default;

//toast
$toastIconFontSize:2rem !default;
$toastMessageTextMargin:0 0 0 1rem !default;
$toastMargin:0 0 1rem 0 !default;
$toastPadding:1rem !default;
$toastBorderWidth:0 !default;
$toastShadow:0 0.25rem 0.75rem rgba(0,0,0,.1) !default;
$toastOpacity:1 !default;
$toastTitleFontWeight:700 !default;
$toastDetailMargin:$inlineSpacing 0 0 0 !default;

//severities
$infoMessageBg:#cce5ff !default;
$infoMessageBorder:solid #b8daff !default;
$infoMessageTextColor:#004085 !default;
$infoMessageIconColor:#004085 !default;
$successMessageBg:#d4edda !default;
$successMessageBorder:solid #c3e6cb !default;
$successMessageTextColor:#155724 !default;
$successMessageIconColor:#155724 !default;
$warningMessageBg:#fff3cd !default;
$warningMessageBorder:solid #ffeeba !default;
$warningMessageTextColor:#856404 !default;
$warningMessageIconColor:#856404 !default;
$errorMessageBg:#f8d7da !default;
$errorMessageBorder:solid #f5c6cb !default;
$errorMessageTextColor:#721c24 !default;
$errorMessageIconColor:#721c24 !default;

//overlays
$overlayContentBorder:1px solid $shade600 !default;
$overlayContentBg:$panelContentBg !default;
$overlayContainerShadow:none !default;

//dialog
$dialogHeaderBg:$shade800 !default;
$dialogHeaderBorder:1px solid $shade600 !default;
$dialogHeaderTextColor:$shade000 !default;
$dialogHeaderFontWeight:600 !default;
$dialogHeaderFontSize:1.25rem !default;
$dialogHeaderPadding:1rem !default;
$dialogContentPadding:1rem !default;
$dialogFooterBorder:1px solid $shade600 !default;
$dialogFooterPadding:1rem !default;

//confirmpopup
$confirmPopupContentPadding:$panelContentPadding !default;
$confirmPopupFooterPadding:0 1.25rem 1.25rem 1.25rem !default;

//tooltip
$tooltipBg:$shade600 !default;
$tooltipTextColor:$shade000 !default;
$tooltipPadding:$inputPadding !default;

//steps
$stepsItemBg:transparent !default;
$stepsItemBorder:1px solid $shade600 !default;
$stepsItemTextColor:$shade100 !default;
$stepsItemNumberWidth:2rem !default;
$stepsItemNumberHeight:2rem !default;
$stepsItemNumberFontSize:1.143rem !default;
$stepsItemNumberColor:$shade000 !default;
$stepsItemNumberBorderRadius:$borderRadius !default;
$stepsItemActiveFontWeight:600 !default;

//progressbar, progressspinner
$progressBarHeight:1.5rem !default;
$progressBarBorder:0 none !default;
$progressBarBg:$shade600 !default;
$progressBarValueBg:$primaryColor !default;
$progressBarValueTextColor:$primaryTextColor !default;

$progressSpinnerStrokeColor:$errorMessageTextColor !default;
$progressSpinnerColorOne:$errorMessageTextColor !default;
$progressSpinnerColorTwo:$infoMessageTextColor !default;
$progressSpinnerColorThree:$successMessageTextColor !default;
$progressSpinnerColorFour:$warningMessageTextColor !default;

//menu (e.g. menu, menubar, tieredmenu)
$menuWidth:12.5rem !default;
$menuBg:$shade800 !default;
$menuBorder:1px solid $shade600 !default;
$menuTextColor:$shade000 !default;
$menuitemPadding:.75rem 1rem !default;
$menuitemBorderRadius:0 !default;
$menuitemTextColor:$shade000 !default;
$menuitemIconColor:$shade100 !default;
$menuitemTextHoverColor:$shade000 !default;
$menuitemIconHoverColor:$shade000 !default;
$menuitemHoverBg:$hoverBg !default;
$menuitemTextFocusColor:$shade000 !default;
$menuitemIconFocusColor:$shade000 !default;
$menuitemFocusBg:rgba(255,255,255,.12) !default;
$menuitemTextActiveColor:$shade000 !default;
$menuitemIconActiveColor:$shade000 !default;
$menuitemActiveBg:$shade900 !default;
$menuitemActiveFocusBg:$shade900 !default;
$menuitemSubmenuIconFontSize:.875rem !default;
$submenuHeaderMargin:0 !default;
$submenuHeaderPadding:.75rem 1rem !default;
$submenuHeaderBg:$shade800 !default;
$submenuHeaderTextColor:$shade000 !default;
$submenuHeaderBorderRadius:0 !default;
$submenuHeaderFontWeight:600 !default;
$overlayMenuBg:$menuBg !default;
$overlayMenuBorder:1px solid $shade600 !default;
$overlayMenuShadow:none !default;
$verticalMenuPadding:.5rem 0 !default;
$menuSeparatorMargin:.5rem 0 !default;

$breadcrumbPadding:1rem !default;
$breadcrumbBg:$shade700 !default;
$breadcrumbBorder:0 none !default;
$breadcrumbItemTextColor:$primaryColor !default;
$breadcrumbItemIconColor:$primaryColor !default;
$breadcrumbLastItemTextColor:$shade000 !default;
$breadcrumbLastItemIconColor:$shade000 !default;
$breadcrumbSeparatorColor:$shade000 !default;

$horizontalMenuPadding:.5rem 1rem !default;
$horizontalMenuBg:$shade700 !default;
$horizontalMenuBorder:0 none !default;
$horizontalMenuTextColor:$shade100 !default;
$horizontalMenuRootMenuitemPadding:1rem !default;
$horizontalMenuRootMenuitemBorderRadius:$borderRadius !default;
$horizontalMenuRootMenuitemTextColor:$shade100 !default;
$horizontalMenuRootMenuitemIconColor:$shade100 !default;
$horizontalMenuRootMenuitemTextHoverColor:$shade000 !default;
$horizontalMenuRootMenuitemIconHoverColor:$shade000 !default;
$horizontalMenuRootMenuitemHoverBg:transparent !default;
$horizontalMenuRootMenuitemTextActiveColor:$shade000 !default;
$horizontalMenuRootMenuitemIconActiveColor:$shade000 !default;
$horizontalMenuRootMenuitemActiveBg:transparent !default;

//badge and tag
$badgeBg:$primaryColor !default;
$badgeTextColor:$primaryTextColor !default;
$badgeMinWidth:1.5rem !default;
$badgeHeight:1.5rem !default;
$badgeFontWeight:700 !default;
$badgeFontSize:.75rem !default;

$tagPadding:.25rem .4rem !default;

//carousel
$carouselIndicatorsPadding:1rem !default;
$carouselIndicatorBg:$shade600 !default;
$carouselIndicatorHoverBg:$hoverBg !default;
$carouselIndicatorBorderRadius:0 !default;
$carouselIndicatorWidth:2rem !default;
$carouselIndicatorHeight:.5rem !default;

//galleria
$galleriaMaskBg:rgba(0,0,0,0.9) !default;
$galleriaCloseIconMargin:.5rem !default;
$galleriaCloseIconFontSize:2rem !default;
$galleriaCloseIconBg:transparent !default;
$galleriaCloseIconColor:$shade100 !default;
$galleriaCloseIconHoverBg:rgba(255,255,255,0.1) !default;
$galleriaCloseIconHoverColor:$shade000 !default;
$galleriaCloseIconWidth:4rem !default;
$galleriaCloseIconHeight:4rem !default;
$galleriaCloseIconBorderRadius:$borderRadius !default;

$galleriaItemNavigatorBg:transparent !default;
$galleriaItemNavigatorColor:$shade100 !default;
$galleriaItemNavigatorMargin:0 .5rem !default;
$galleriaItemNavigatorFontSize:2rem !default;
$galleriaItemNavigatorHoverBg:rgba(255,255,255,0.1) !default;
$galleriaItemNavigatorHoverColor:$shade100 !default;
$galleriaItemNavigatorWidth:4rem !default;
$galleriaItemNavigatorHeight:4rem !default;
$galleriaItemNavigatorBorderRadius:$borderRadius !default;

$galleriaCaptionBg:rgba(0,0,0,.5) !default;
$galleriaCaptionTextColor:$shade100 !default;
$galleriaCaptionPadding:1rem !default;

$galleriaIndicatorsPadding:1rem !default;
$galleriaIndicatorBg:$shade200 !default;
$galleriaIndicatorHoverBg:$shade300 !default;
$galleriaIndicatorBorderRadius:$borderRadius !default;
$galleriaIndicatorWidth:1rem !default;
$galleriaIndicatorHeight:1rem !default;
$galleriaIndicatorsBgOnItem:rgba(0,0,0,.5) !default;
$galleriaIndicatorBgOnItem:rgba(255,255,255,.4) !default;
$galleriaIndicatorHoverBgOnItem:rgba(255,255,255,.6) !default;

$galleriaThumbnailContainerBg:rgba(0,0,0,.9) !default;
$galleriaThumbnailContainerPadding:1rem .25rem !default;
$galleriaThumbnailNavigatorBg:transparent !default;
$galleriaThumbnailNavigatorColor:$shade100 !default;
$galleriaThumbnailNavigatorHoverBg:rgba(255,255,255,0.1) !default;
$galleriaThumbnailNavigatorHoverColor:$shade100 !default;
$galleriaThumbnailNavigatorBorderRadius:$borderRadius !default;
$galleriaThumbnailNavigatorWidth:2rem !default;
$galleriaThumbnailNavigatorHeight:2rem !default;

//divider
$dividerHorizontalMargin:1rem 0 !default;
$dividerHorizontalPadding:0 1rem !default;
$dividerVerticalMargin:0 1rem !default;
$dividerVerticalPadding:1rem 0 !default;
$dividerSize:1px !default;
$dividerColor:$shade600 !default;

//avatar
$avatarBg:$shade600 !default;
$avatarTextColor:$textColor !default;

//chip
$chipBg:$shade600 !default;
$chipTextColor:$textColor !default;
$chipBorderRadius: 16px !default;
$chipFocusBg:$shade500 !default;
$chipFocusTextColor:$textColor !default;

//scrollTop
$scrollTopBg:$primaryColor !default;
$scrollTopHoverBg:$primaryDarkColor !default;
$scrollTopWidth:3rem !default;
$scrollTopHeight:3rem !default;
$scrollTopBorderRadius:$borderRadius !default;
$scrollTopFontSize:1.5rem !default;
$scrollTopTextColor:$highlightTextColor !default;

//skeleton
$skeletonBg:rgba(255,255,255,.06) !default;
$skeletonAnimationBg:rgba(255,255,255,.04) !default;

//splitter
$splitterGutterBg:rgba(255,255,255,.04) !default;
$splitterGutterHandleBg:$shade600 !default;

//speeddial
$speedDialButtonWidth: 4rem !default;
$speedDialButtonHeight: 4rem !default;
$speedDialButtonIconFontSize: 1.3rem !default;
$speedDialActionWidth: 3rem !default;
$speedDialActionHeight: 3rem !default;
$speedDialActionBg: $shade700 !default;
$speedDialActionHoverBg: $shade600 !default;
$speedDialActionTextColor: #fff !default;
$speedDialActionTextHoverColor: #fff !default;

//dock
$dockActionWidth: 4rem !default;
$dockActionHeight: 4rem !default;
$dockItemPadding: .5rem !default;
$dockItemBorderRadius:$borderRadius !default;
$dockCurrentItemMargin: 1.5rem !default;
$dockFirstItemsMargin: 1.3rem !default;
$dockSecondItemsMargin: 0.9rem !default;
$dockBg: rgba(255,255,255,.1) !default;
$dockBorder: 1px solid rgba(255,255,255,0.2) !default;
$dockPadding: .5rem .5rem !default;
$dockBorderRadius: .5rem !default;

//image
$imageMaskBg:rgba(0,0,0,0.9) !default;
$imagePreviewToolbarPadding:1rem !default;
$imagePreviewIndicatorColor:#f8f9fa !default;
$imagePreviewIndicatorBg:rgba(0,0,0,0.5) !default;
$imagePreviewActionIconBg:transparent !default;
$imagePreviewActionIconColor:#f8f9fa !default;
$imagePreviewActionIconHoverBg:rgba(255,255,255,0.1) !default;
$imagePreviewActionIconHoverColor:#f8f9fa !default;
$imagePreviewActionIconWidth:3rem !default;
$imagePreviewActionIconHeight:3rem !default;
$imagePreviewActionIconFontSize:1.5rem !default;
$imagePreviewActionIconBorderRadius:50% !default;

:root {
    font-family:-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol" ;
    --font-family:-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol" ;
    --surface-a:#{$shade800};
    --surface-b:#{$shade900};
    --surface-c:#{$hoverBg};
    --surface-d:#{$shade600};
    --surface-e:#{$shade800};
    --surface-f:#{$shade800};
    --text-color:#{$shade000};
    --text-color-secondary:#{$shade100};
    --primary-color:#{$primaryColor};
    --primary-color-text:#{$primaryTextColor};
    --surface-0: #20262e;
    --surface-50: #363c43;
    --surface-100: #4d5158;
    --surface-200: #63676d;
    --surface-300: #797d82;
    --surface-400: #909397;
    --surface-500: #a6a8ab;
    --surface-600: #bcbec0;
    --surface-700: #d2d4d5;
    --surface-800: #e9e9ea;
    --surface-900: #ffffff;
    --gray-50:#e9e9ea;
    --gray-100: #d2d4d5;
    --gray-200: #bcbec0;
    --gray-300: #a6a8ab;
    --gray-400: #909397;
    --gray-500: #797d82;
    --gray-600: #63676d;
    --gray-700: #4d5158;
    --gray-800: #363c43;
    --gray-900: #20262e;
    --content-padding:#{$panelContentPadding};
    --inline-spacing:#{$inlineSpacing};
    --border-radius:#{$borderRadius};
    --surface-ground:#20262e;
    --surface-section:#20262e;
    --surface-card:#2a323d;
    --surface-overlay:#2a323d;
    --surface-border:#3f4b5b;
    --surface-hover:rgba(255,255,255,.04);
    --focus-ring: #{$focusShadow};
    --maskbg: #{$maskBg};
    --highlight-bg: #{$highlightBg};
    --highlight-text-color: #{$highlightTextColor};
    color-scheme: dark;
}
