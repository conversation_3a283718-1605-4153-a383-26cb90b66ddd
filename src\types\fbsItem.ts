export interface FbsItemModel {
  itemID?: string;
  batchID?: string;
  achFilesId?: string;
  auxOnUs?: string;
  bankNo?: string;
  branchCode?: string;
  chqAcNo?: string;
  serNo?: string;
  chqAmount?: number;
  chqDate?: string;
  currText?: string;
  custAcNo?: string;
  docType?: string;
  exceptionMessage?: string;
  fbsDate?: string;
  fcRefKey?: string;
  fwInd?: string;
  iflMatchInd?: number;
  micrLine?: string;
  onUs?: string;
  procDate?: string;
  routeNo?: string;
  selected: boolean;
  status?: number;
  version?: number;
  x9FileName?: string;
  x937Ind?: number;
}

export interface FbsImageModel {
  itemId?: string;
  frontImage?: string;
  rearImage?: string;
  imageData?: any;
}
