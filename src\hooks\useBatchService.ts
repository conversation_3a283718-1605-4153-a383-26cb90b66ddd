import { useState } from 'react';
import { apiClient } from '../services/apiService';
import { BatchModel } from '../types/fbsBatch';

export const useBatchService = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getBatchData = async (fbsDate: string): Promise<BatchModel[]> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.get<BatchModel[]>(`batchList/${fbsDate}`);
      return response.data;
    } catch (err) {
      setError('Failed to fetch batch data');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const getBatches = async (): Promise<BatchModel[]> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.get<BatchModel[]>('batchList');
      return response.data;
    } catch (err) {
      setError('Failed to fetch batches');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    getBatchData,
    getBatches,
    loading,
    error
  };
};
