export interface ItemModel {
  recID?: number;
  nchFileName?: string;
  batchId?: string;
  tranCode?: number;
  routeNo?: string;
  acctNo?: string;
  acctType?: string;
  indvId?: string;
  indvName?: string;
  descDate?: string;
  entryAmt?: number;
  traceNbr?: string;
  effDate?: Date;
  procDate?: Date;
  addnDet?: string;
  retInd?: number;
  retRCode?: string;
  checkBox?: boolean;
}

export interface FilesModel {
  recId?: number;
  fileName?: string;
  fileDate?: Date;
  batchCount?: number;
  itemCount?: number;
  destName?: string;
  origName?: string;
  creditTotal?: number;
  debitTotal?: number;
  fileType?: string;
  procTime?: string;
  fileStatus?: string;
  recInd?: number;
  isSelected?: boolean;
}

export interface RespMessage {
  message?: string;
  status?: string;
  data?: any;
}

export interface IflUnmatchModel {
  iflRefNo?: string;
  itemId?: string;
  routeNo?: string;
  chqSerNo?: string;
  chqAmount?: number;
  chqDate?: Date;
  chqAcct?: string;
  benAcct?: string;
  tranDate?: Date;
  tranTime?: Date;
  trnBranch?: string;
}

export interface IflTransModel {
  tranRefno?: string;
  routingNumber?: string;
  serialNo?: string;
  chqAmount?: number;
  chqDate?: Date;
  chqAccount?: string;
  beneficiaryAccount?: string;
  tranDate?: Date;
  tranTime?: Date;
  tranBranch?: string;
  tranTellerID?: string;
  fbMatchInd?: number;
  fbsItemId?: string;
  x9OutInd?: number;
  x9OutDate?: Date;
  fbMatchDate?: Date;
  exceptionMessage?: string;
}

export interface ExpandedRows {
  [key: string]: boolean;
}

export interface ToastMessage {
  severity: 'success' | 'info' | 'warn' | 'error';
  summary?: string;
  detail?: string;
  life?: number;
}

export interface DupItemsModel {
  itemId?: string;
  batchId?: string;
  routeNo?: string;
  chqSerNo?: string;
  chqAmount?: number;
  chqDate?: Date;
  chqAcct?: string;
  dupCount?: number;
  selected?: boolean;
}

export interface X9ChqModel {
  itemId?: string;
  batchId?: string;
  routeNo?: string;
  chqSerNo?: string;
  chqAmount?: number;
  chqDate?: Date;
  chqAcct?: string;
  x9Status?: string;
  selected?: boolean;
}
