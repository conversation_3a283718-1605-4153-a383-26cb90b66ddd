import React from 'react';
import { BrowserRouter, Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { SimpleLogin } from './pages/SimpleLogin';

// Import PrimeReact CSS
import 'primereact/resources/themes/lara-light-blue/theme.css';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import 'primeflex/primeflex.css';

// Simple test components first
const LoginTest = () => {
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleTestLogin = async () => {
    const user = await login('admin', 'password');
    if (user) {
      navigate('/dashboard');
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>Login Page</h2>
      <p>Test login with admin/password</p>
      <button onClick={handleTestLogin}>Test Login</button>
    </div>
  );
};

const DashboardTest = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>Dashboard</h2>
      <p>Welcome, {user?.name}!</p>
      <button onClick={handleLogout}>Logout</button>
    </div>
  );
};

function App() {
  return (
    <AuthProvider>
      <BrowserRouter>
        <div>
          <h1>BOB Image Solutions - React Migration</h1>
          <Routes>
            <Route path="/login" element={<SimpleLogin />} />
            <Route path="/dashboard" element={<DashboardTest />} />
            <Route path="/" element={<Navigate to="/login" replace />} />
          </Routes>
        </div>
      </BrowserRouter>
    </AuthProvider>
  );
}

export default App;
