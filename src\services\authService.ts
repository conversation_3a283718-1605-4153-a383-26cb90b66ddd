// src/services/authService.ts
import { type User } from "../interfaces/user";
import userService from "./userService";

class AuthService {
  private isLogged: boolean = false;

  login(username: string, password: string): User | undefined {
    const user = userService.users.find(
      (u) => u.username === username && u.password === password
    );

    this.isLogged = user !== undefined;
    return user;
  }

  logout(): void {
    this.isLogged = false;
  }

  isAuthenticated(): boolean {
    return this.isLogged;
  }
}

// export singleton instance
export default new AuthService();
