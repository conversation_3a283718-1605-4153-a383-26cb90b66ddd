<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1440" height="380" viewBox="0 0 1440 380">
    <defs>
        <linearGradient id="a" x1="50%" x2="50%" y1="0%" y2="100%">
            <stop offset="0%" stop-color="#F7E7BF"/>
            <stop offset="100%" stop-color="#EBCB8B"/>
        </linearGradient>
        <linearGradient id="d" x1="50%" x2="50%" y1="0%" y2="100%">
            <stop offset="0%" stop-color="#EABCA9"/>
            <stop offset="100%" stop-color="#D08770"/>
        </linearGradient>
        <path id="c" d="M0 107c225.333 61.333 364.333 92 417 92 79 0 194-79.5 293-79.5S914 244 1002 244s156-45 195-68.5c26-15.667 107-74.167 243-175.5v357.5H0V107z"/>
        <filter id="b" width="105.1%" height="120.7%" x="-2.6%" y="-10.9%" filterUnits="objectBoundingBox">
            <feOffset dy="-2" in="SourceAlpha" result="shadowOffsetOuter1"/>
            <feGaussianBlur in="shadowOffsetOuter1" result="shadowBlurOuter1" stdDeviation="12"/>
            <feColorMatrix in="shadowBlurOuter1" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.11 0"/>
        </filter>
    </defs>
    <g fill="none" fill-rule="evenodd">
        <path fill="url(#a)" d="M0 202c142.333-66.667 249-90 320-70 106.5 30 122 83.5 195 83.5h292c92.642-106.477 190.309-160.81 293-163 102.691-2.19 216.025 47.643 340 149.5v155.5H0V202z" transform="translate(0 22.5)"/>
        <g transform="translate(0 22.5)">
            <use fill="#000" filter="url(#b)" xlink:href="#c"/>
            <use fill="url(#d)" xlink:href="#c"/>
        </g>
    </g>
</svg>
