import { useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

export const useIdleTimer = () => {
  const navigate = useNavigate();
  const { logout } = useAuth();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isActiveRef = useRef(false);

  const resetTimer = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  const startTimer = useCallback((minutes: number) => {
    resetTimer();
    isActiveRef.current = true;
    
    const timeoutDuration = minutes * 60 * 1000; // Convert to milliseconds
    
    timeoutRef.current = setTimeout(() => {
      if (isActiveRef.current) {
        logout();
        navigate('/login');
        console.log('Session expired due to inactivity');
      }
    }, timeoutDuration);

    // Add event listeners for user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    const resetOnActivity = () => {
      if (isActiveRef.current) {
        resetTimer();
        startTimer(minutes);
      }
    };

    events.forEach(event => {
      document.addEventListener(event, resetOnActivity, true);
    });

    // Cleanup function
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, resetOnActivity, true);
      });
    };
  }, [logout, navigate, resetTimer]);

  const stopTimer = useCallback(() => {
    isActiveRef.current = false;
    resetTimer();
  }, [resetTimer]);

  useEffect(() => {
    return () => {
      stopTimer();
    };
  }, [stopTimer]);

  return {
    startTimer,
    stopTimer,
    resetTimer
  };
};
