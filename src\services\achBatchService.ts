// src/services/achBatchService.ts
import axios from "axios";
import { type AchBatchModel } from "../interfaces/achBatch";
import { type ItemModel } from "../interfaces/items";
import { environment } from "../environments/environment";

class AchBatchService {
  private urlStr = "";

  async fetchBatchData(achDate: string): Promise<AchBatchModel[]> {
    this.urlStr = `${environment.baseUrl}nchbatchlistIB/${achDate}`;
    const response = await axios.get<AchBatchModel[]>(this.urlStr);
    return response.data;
  }

  async fetchPendingBatchData(): Promise<AchBatchModel[]> {
    this.urlStr = `${environment.baseUrl}nchbatchIB/pending`;
    const response = await axios.get<AchBatchModel[]>(this.urlStr);
    return response.data;
  }

  async fetchBatchItems(batchRecKey: string | number): Promise<ItemModel[]> {
    this.urlStr = `${environment.baseUrl}nchbatch/items/${batchRecKey}`;
    const response = await axios.get<ItemModel[]>(this.urlStr);
    return response.data;
  }

  async fetchACHItems(achDate: string): Promise<ItemModel[]> {
    this.urlStr = `${environment.baseUrl}nchbatch/items/${achDate}`;
    const response = await axios.get<ItemModel[]>(this.urlStr);
    return response.data;
  }
}

export default new AchBatchService();
