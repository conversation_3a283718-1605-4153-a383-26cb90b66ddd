// src/services/achBatchService.ts
import axios from "axios";
import { batchModel } from "../interfaces/batch";
import { itemModel } from "../interfaces/items";
import { environment } from "../environments/environment";

class AchBatchService {
  private urlStr = "";

  async fetchBatchData(achDate: string): Promise<batchModel[]> {
    this.urlStr = `${environment.baseUrl}nchbatchlistIB/${achDate}`;
    const response = await axios.get<batchModel[]>(this.urlStr);
    return response.data;
  }

  async fetchPendingBatchData(): Promise<batchModel[]> {
    this.urlStr = `${environment.baseUrl}nchbatchIB/pending`;
    const response = await axios.get<batchModel[]>(this.urlStr);
    return response.data;
  }

  async fetchBatchItems(batchRecKey: string | number): Promise<itemModel[]> {
    this.urlStr = `${environment.baseUrl}nchbatch/items/${batchRecKey}`;
    const response = await axios.get<itemModel[]>(this.urlStr);
    return response.data;
  }

  async fetchACHItems(achDate: string): Promise<itemModel[]> {
    this.urlStr = `${environment.baseUrl}nchbatch/items/${achDate}`;
    const response = await axios.get<itemModel[]>(this.urlStr);
    return response.data;
  }
}

export default new AchBatchService();
