import React from 'react';
import { Outlet } from 'react-router-dom';

interface DesktopLayoutProps {
  children?: React.ReactNode;
}

export const DesktopLayout: React.FC<DesktopLayoutProps> = ({ children }) => {
  return (
    <div className="layout-container">
      {/* Topbar component will go here */}
      <div className="topbar">
        <h3>BOB Image Solutions</h3>
        {/* Add topbar navigation */}
      </div>
      
      {/* Sidebar component will go here */}
      <div className="sidebar">
        {/* Add sidebar navigation */}
      </div>

      <div className="layout-content-wrapper">
        <div className="layout-content">
          {children || <Outlet />}
        </div>
      </div>
    </div>
  );
};
